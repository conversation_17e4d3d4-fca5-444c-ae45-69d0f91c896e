[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file vocab.json
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file merges.txt
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file tokenizer.json
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file added_tokens.json
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file special_tokens_map.json
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file tokenizer_config.json
[INFO|2025-08-11 20:27:31] tokenization_utils_base.py:2065 >> loading file chat_template.jinja
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2336 >> Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.
[INFO|2025-08-11 20:27:32] configuration_utils.py:750 >> loading configuration file D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat\config.json
[INFO|2025-08-11 20:27:32] configuration_utils.py:817 >> Model config Qwen2Config {
  "architectures": [
    "Qwen2ForCausalLM"
  ],
  "attention_dropout": 0.0,
  "bos_token_id": 151643,
  "eos_token_id": 151645,
  "hidden_act": "silu",
  "hidden_size": 2048,
  "initializer_range": 0.02,
  "intermediate_size": 5504,
  "layer_types": [
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention"
  ],
  "max_position_embeddings": 32768,
  "max_window_layers": 21,
  "model_type": "qwen2",
  "num_attention_heads": 16,
  "num_hidden_layers": 24,
  "num_key_value_heads": 16,
  "rms_norm_eps": 1e-06,
  "rope_scaling": null,
  "rope_theta": 1000000.0,
  "sliding_window": null,
  "tie_word_embeddings": false,
  "torch_dtype": "bfloat16",
  "transformers_version": "4.55.0",
  "use_cache": true,
  "use_sliding_window": false,
  "vocab_size": 151936
}

[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file vocab.json
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file merges.txt
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file tokenizer.json
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file added_tokens.json
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file special_tokens_map.json
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file tokenizer_config.json
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2065 >> loading file chat_template.jinja
[INFO|2025-08-11 20:27:32] tokenization_utils_base.py:2336 >> Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.
[INFO|2025-08-11 20:27:32] logging.py:143 >> Loading dataset alpaca_output.jsonl...
[INFO|2025-08-11 20:27:58] configuration_utils.py:750 >> loading configuration file D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat\config.json
[INFO|2025-08-11 20:27:58] configuration_utils.py:817 >> Model config Qwen2Config {
  "architectures": [
    "Qwen2ForCausalLM"
  ],
  "attention_dropout": 0.0,
  "bos_token_id": 151643,
  "eos_token_id": 151645,
  "hidden_act": "silu",
  "hidden_size": 2048,
  "initializer_range": 0.02,
  "intermediate_size": 5504,
  "layer_types": [
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention",
    "full_attention"
  ],
  "max_position_embeddings": 32768,
  "max_window_layers": 21,
  "model_type": "qwen2",
  "num_attention_heads": 16,
  "num_hidden_layers": 24,
  "num_key_value_heads": 16,
  "rms_norm_eps": 1e-06,
  "rope_scaling": null,
  "rope_theta": 1000000.0,
  "sliding_window": null,
  "tie_word_embeddings": false,
  "torch_dtype": "bfloat16",
  "transformers_version": "4.55.0",
  "use_cache": true,
  "use_sliding_window": false,
  "vocab_size": 151936
}

[INFO|2025-08-11 20:27:58] logging.py:143 >> KV cache is disabled during training.
[INFO|2025-08-11 20:27:59] modeling_utils.py:1305 >> loading weights file D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat\model.safetensors
[INFO|2025-08-11 20:27:59] modeling_utils.py:2411 >> Instantiating Qwen2ForCausalLM model under default dtype torch.bfloat16.
[INFO|2025-08-11 20:27:59] configuration_utils.py:1098 >> Generate config GenerationConfig {
  "bos_token_id": 151643,
  "eos_token_id": 151645,
  "use_cache": false
}

[INFO|2025-08-11 20:28:02] modeling_utils.py:5606 >> All model checkpoint weights were used when initializing Qwen2ForCausalLM.

[INFO|2025-08-11 20:28:02] modeling_utils.py:5614 >> All the weights of Qwen2ForCausalLM were initialized from the model checkpoint at D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat.
If your task is similar to the task the model of the checkpoint was trained on, you can already use Qwen2ForCausalLM for predictions without further training.
[INFO|2025-08-11 20:28:02] configuration_utils.py:1051 >> loading configuration file D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat\generation_config.json
[INFO|2025-08-11 20:28:02] configuration_utils.py:1098 >> Generate config GenerationConfig {
  "bos_token_id": 151643,
  "do_sample": true,
  "eos_token_id": [
    151645,
    151643
  ],
  "pad_token_id": 151643,
  "repetition_penalty": 1.1,
  "top_p": 0.8
}

[INFO|2025-08-11 20:28:02] logging.py:143 >> Gradient checkpointing enabled.
[INFO|2025-08-11 20:28:02] logging.py:143 >> Using torch SDPA for faster training and inference.
[INFO|2025-08-11 20:28:02] logging.py:143 >> Upcasting trainable params to float32.
[INFO|2025-08-11 20:28:02] logging.py:143 >> Fine-tuning method: LoRA
[INFO|2025-08-11 20:28:02] logging.py:143 >> Found linear modules: q_proj,k_proj,up_proj,gate_proj,down_proj,v_proj,o_proj
[INFO|2025-08-11 20:28:03] logging.py:143 >> trainable params: 7,495,680 || all params: 1,844,324,352 || trainable%: 0.4064
[INFO|2025-08-11 20:28:03] trainer.py:757 >> Using auto half precision backend
[INFO|2025-08-11 20:28:03] trainer.py:2433 >> ***** Running training *****
[INFO|2025-08-11 20:28:03] trainer.py:2434 >>   Num examples = 1,807
[INFO|2025-08-11 20:28:03] trainer.py:2435 >>   Num Epochs = 3
[INFO|2025-08-11 20:28:03] trainer.py:2436 >>   Instantaneous batch size per device = 2
[INFO|2025-08-11 20:28:03] trainer.py:2439 >>   Total train batch size (w. parallel, distributed & accumulation) = 16
[INFO|2025-08-11 20:28:03] trainer.py:2440 >>   Gradient Accumulation steps = 8
[INFO|2025-08-11 20:28:03] trainer.py:2441 >>   Total optimization steps = 339
[INFO|2025-08-11 20:28:03] trainer.py:2442 >>   Number of trainable parameters = 7,495,680
[INFO|2025-08-11 20:31:47] logging.py:143 >> {'loss': 2.7773, 'learning_rate': 4.9983e-05, 'epoch': 0.04, 'throughput': 401.01}
[INFO|2025-08-11 20:35:58] logging.py:143 >> {'loss': 2.7609, 'learning_rate': 4.9913e-05, 'epoch': 0.09, 'throughput': 383.29}
