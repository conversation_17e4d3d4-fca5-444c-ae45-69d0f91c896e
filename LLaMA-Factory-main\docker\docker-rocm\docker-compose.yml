services:
  llamafactory:
    build:
      dockerfile: ./docker/docker-rocm/Dockerfile
      context: ../..
      args:
        PIP_INDEX: https://pypi.org/simple
        EXTRAS: metrics
    container_name: llamafactory
    ports:
      - "7860:7860"
      - "8000:8000"
    ipc: host
    tty: true
    # shm_size: "16gb"  # ipc: host is set
    stdin_open: true
    command: bash
    devices:
      - /dev/kfd:/dev/kfd
      - /dev/dri:/dev/dri
    restart: unless-stopped
