### model
model_name_or_path: D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat
trust_remote_code: true

### method
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

### dataset
dataset: identity,alpaca_en_demo
template: qwen
cutoff_len: 1024
max_samples: 500
overwrite_cache: true
# 内存优化设置
preprocessing_num_workers: 1  # 禁用多进程
preprocessing_batch_size: 50  # 减小批处理大小
dataloader_num_workers: 0     # 禁用数据加载器多进程

### output
output_dir: saves/qwen1.5-1.8b/lora/sft
logging_steps: 10
save_steps: 500
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none

### training
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
learning_rate: 5e-4
num_train_epochs: 3
lr_scheduler_type: cosine
warmup_steps: 100
fp16: true
