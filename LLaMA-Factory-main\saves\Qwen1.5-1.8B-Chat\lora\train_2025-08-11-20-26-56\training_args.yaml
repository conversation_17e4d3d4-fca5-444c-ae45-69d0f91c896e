bf16: true
cutoff_len: 2048
dataset: alpaca_output
dataset_dir: data
ddp_timeout: 180000000
do_train: true
enable_thinking: true
eval_steps: 100
eval_strategy: steps
finetuning_type: lora
flash_attn: auto
gradient_accumulation_steps: 8
include_num_input_tokens_seen: true
learning_rate: 5.0e-05
logging_steps: 5
lora_alpha: 16
lora_dropout: 0
lora_rank: 8
lora_target: all
lr_scheduler_type: cosine
max_grad_norm: 1.0
max_samples: 100000
model_name_or_path: D:\pythonProject\llms\Qwen1_5_1_8B_Chat\Qwen\Qwen1___5-1___8B-Chat
num_train_epochs: 3.0
optim: adamw_torch
output_dir: saves\Qwen1.5-1.8B-Chat\lora\train_2025-08-11-20-26-56
packing: false
per_device_eval_batch_size: 2
per_device_train_batch_size: 2
plot_loss: true
preprocessing_num_workers: 3
report_to: none
save_steps: 100
stage: sft
template: qwen
trust_remote_code: true
val_size: 0.1
warmup_steps: 0
