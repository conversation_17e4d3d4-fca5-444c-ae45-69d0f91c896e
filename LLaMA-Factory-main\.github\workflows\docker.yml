name: docker

on:
  workflow_dispatch:
  push:
    branches:
      - "main"
    paths:
      - "**/*.py"
      - "requirements.txt"
      - "docker/**"
      - ".github/workflows/*.yml"
  pull_request:
    branches:
      - "main"
    paths:
      - "**/*.py"
      - "requirements.txt"
      - "docker/**"
      - ".github/workflows/*.yml"

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        device:
          - "cuda"
          - "npu"

    runs-on: ubuntu-latest

    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}-${{ matrix.device }}
      cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

    environment:
      name: docker
      url: https://hub.docker.com/r/hiyouga/llamafactory

    steps:
      - name: Free up disk space
        uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be # v1.3.1
        with:
          tool-cache: true
          docker-images: false

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Get llamafactory version
        id: version
        run: |
          echo "tag=$(python setup.py --version | sed 's/\.dev0//')" >> "$GITHUB_OUTPUT"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        if: ${{ github.event_name != 'pull_request' }}
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Login to Quay
        if: ${{ github.event_name != 'pull_request' && matrix.device == 'npu' }}
        uses: docker/login-action@v3
        with:
          registry: quay.io
          username: ${{ vars.QUAY_ASCEND_USERNAME }}
          password: ${{ secrets.QUAY_ASCEND_TOKEN }}

      - name: Build and push Docker image (CUDA)
        if: ${{ matrix.device == 'cuda' }}
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/docker-cuda/Dockerfile
          build-args: |
            EXTRAS=metrics,deepspeed,liger-kernel
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            docker.io/hiyouga/llamafactory:latest
            docker.io/hiyouga/llamafactory:${{ steps.version.outputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Docker image (NPU)
        if: ${{ matrix.device == 'npu' }}
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          file: ./docker/docker-npu/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            docker.io/hiyouga/llamafactory:latest-npu-a2
            docker.io/hiyouga/llamafactory:${{ steps.version.outputs.tag }}-npu-a2
            quay.io/ascend/llamafactory:latest-npu-a2
            quay.io/ascend/llamafactory:${{ steps.version.outputs.tag }}-npu-a2
          cache-from: type=gha
          cache-to: type=gha,mode=max
